

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>控制台主页一</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="__PUBLIC__/admin/css/admin.css" media="all">
</head>
<body>
  
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-sm6 layui-col-md3">
       
      </div>
   

      
      
	 
	  
	  
	  
	  
	  
      <div class="layui-col-md12">
        <div class="layui-row layui-col-space15">
         

          
		  
		  
		  <div class="layui-col-md6">
            <div class="layui-card">
              <div class="layui-card-header">系统信息</div>
              <div class="layui-card-body">

                <div class="layui-carousel layadmin-carousel layadmin-backlog">
                 
                    <h3>程序版本v1.0</h3>
					<h3>PHP版本：<?php echo PHP_VERSION;?></h3>
						<h3>MYSQL支持：<?php echo function_exists (mysql_close)?"是":"否"; ?>
</h3>
					<h3>操作系统：<?PHP echo PHP_OS; ?></h3>
					<h3>服务器端信息：<?PHP echo $_SERVER ['SERVER_SOFTWARE']; ?>
</h3>   
                    <h3>源码来自：巅峰导航 <a href="https://admin.qidian.qq.com/static_proxy/b2b-qq/wpa-link/index.html#/person?uin=353578408"><span style="color:red;">（点击此处联系）</span></a></h3>
                </div>
              </div>
            </div>
          </div>
		  
		   <div class="layui-col-md6">
            <div class="layui-card">
              <div class="layui-card-header">免责声明</div>
              <div class="layui-card-body">

                <div class="layui-carousel layadmin-carousel layadmin-backlog">
				<span style="color:red;font-size:17px;">使用本源码的用户请勿用于涉黄或其他违反国家法律的用途上，本店仅提供源码框架，不提供任何内容且无法远程控制任意内容，如有客户不遵守国家法律 产生相关法律问题与本店无关，我们不接触一切违反国家法律的技术支持工作，使用本产品后请正确正规经营APP程序!</span>
                </div>
              </div>
            </div>
          </div>
		  
		  
        </div>
      </div>
    </div>
  </div>
  <script src="__PUBLIC__/common/lib/layui/layui.js"></script>
  <script>
    layui.config({
      base: '__PUBLIC__/admin/js/' //静态资源所在路径
    }).extend({
      index: 'lib/index' //主入口模块
    }).use(['index', 'home']);
  </script>
</body>
</html>


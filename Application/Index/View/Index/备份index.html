<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
    <meta name="applicable-device" content="mobile">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta content="width=device-width, initial-scale=1,maximum-scale=1.0,user-scalable=no" name="viewport">
    <meta content="true" name="full-screen">
    <meta content="true" name="x5-fullscreen">
    <meta content="true" name="360-fullscreen">
    <!--iphone qq -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>{$con.webtitle}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <script src="__PUBLIC__/static/js/jquery.js"></script>
    <script src="__PUBLIC__/static/js/jquery.lazyload.js"></script>
    <script src="__PUBLIC__/static/js/layer.js"></script>
    <script src="__PUBLIC__/static/js/cookie.js"></script>
    <script src="__PUBLIC__/static/js/sdk_core.js"></script>

    <link rel="stylesheet" href="__PUBLIC__/static/css/mainnew.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/main2.css">
    <link rel="stylesheet" href="__PUBLIC__/static/css/animations.css">
    <script>layer.config({ skin: 'dialogs' });</script>
 
<style>
    video{
        width:100%;
        height:auto;
    }
    .search {padding:5px;}
    .search .sear_input {width:220px;height:33px;border:1px #ccc solid;border-radius:4px;font-size:14px;padding-left:8px;vertical-align:middle;box-sizing:border-box;-webkit-border-radius:12px;border-radius:12px;}
    .search .btn_input {    position: absolute;
    right: 10px;
    top: 3px;
    width: 30px;
    height: 30px;
    margin: 4px 0 0 150px;
    display: block;
    border: none;
    background: #fff url(__PUBLIC__/static/image/searchbg.png) 5px no-repeat;
    background-size: 18px;
    cursor: pointer;
    -webkit-border-radius: 12px;
    border-radius: 12px;
}}
        .has-border-bottom.bold {
            border-width: 5px;
        }
        .has-border-bottom {
            border-bottom: 1px solid #f4f4f4;
        }
.h5-title[data-v-224c70c0] {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    padding: 5px 0 5px /*padding: 15px 0 5px*/;
}
.h5-title img[data-v-224c70c0] {
    display: inline-block;
    width: 12px;
    vertical-align: middle;
}
.h5-title span[data-v-224c70c0] {
    display: inline-block;
    margin: 0 10px;
    vertical-align: middle;
}
.h5-title img[data-v-224c70c0] {
    display: inline-block;
    width: 12px;
    vertical-align: middle;
}

.welfare-list[data-v-25e1d289] {
    width: auto;
    overflow-x: auto;
    padding: 0px 10px 5px;
    /* padding: 10px 10px 15px; */
    text-align: center;
}
.box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.welfare-list .welfare-item[data-v-25e1d289] {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-right: 10px;
    width: 75x;
}
.app-vertical[data-v-d077d592] {
    text-align: center;
}
.app-vertical .icon[data-v-d077d592] {
    width: 70px;
    margin: 0 auto;
}
.icon {
    text-decoration: none;
    border-bottom: none;
    position: relative;
}
.icon img {
    width: 100%;
    border-radius: 10px;
}
.app-vertical .name[data-v-d077d592] {
    margin: 5px 0;
}
.hide-text.one, .hide-text.two {
    display: -webkit-box;
    -webkit-box-orient: vertical;
}
.hide-text.one {
    -webkit-line-clamp: 1;
}
.hide-text {
    overflow: hidden;
    text-overflow: ellipsis;
}
.app-vertical .label[data-v-d077d592] {
    font-size: 10px;
    border-top-right-radius: 8px;
    border-bottom-left-radius: 8px;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#ff4842), to(#ff7d5e));
    background: linear-gradient(left, #ff4842, #ff7d5e);
    padding: 2.5px 0;
    
}
/*--公告 */
    @font-face {
    font-family: "iconfont"; /* Project id 2516453 */
    src: url("//at.alicdn.com/t/font_2516453_g6qjhhqblt9.woff2?t=1620545333370")
        format("woff2"),
        url("//at.alicdn.com/t/font_2516453_g6qjhhqblt9.woff?t=1620545333370")
        format("woff"),
        url("//at.alicdn.com/t/font_2516453_g6qjhhqblt9.ttf?t=1620545333370")
        format("truetype");
    }

    .iconfont {
        font-family: "iconfont" !important;
        font-size: 16px;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -webkit-text-stroke-width: 0.2px;
        -moz-osx-font-smoothing: grayscale;
    }

    .ad {
        width: 100%;
        height:100%;
        background-color: #fff;
        box-sizing: border-box;
        padding: 10px 10px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 20px;
        color: #353535;
        margin: 10px auto;

        i {
            color: #ff6146;
            font-size: 20px;
            margin-right: 10px;
        }

        .content{
            flex: 1;
            overflow: hidden;
            span {
                display: block;
                width: auto;
                white-space: nowrap;
            }
        }
    }
@keyframes marquee {
        0% {
        transform: translateX(0);
        }
        100% {
        transform: translateX(-100%);
        }
    }

    .content{
        span{
            animation: marquee 30s linear infinite;
        }
    }
</style>

</head>
<body>
    <div class="wrapper">
        <div class="main">
            
            
            <div class="banner" id="slides" style="overflow: hidden;border-radius: 30px;">
                <foreach name="foot" item="vo">	 
                <a href="{$vo.link}"><img src="{$vo.picname}" alt="{$vo.title}"></a>     </foreach>  
            </div>

<div class="wrapper">
    <div class='ad'>
        <i class="iconfont">&#xe633;</i>&nbsp;
        <p class='content'>
            <span><marquee direction=left>{$con.gdgg}</marquee></span>
        </p>
    </div>
</div>
<div class="nav">
    <ul id="tab">
        <li class="tab active"><a href="javascript:;" style="font-size: 90%;color: #E91E63;line-height: 2.5rem;">推荐软件</a></li>
        <li class="tab"><a href="javascript:;" style="font-size: 90%;color: #E91E63;line-height: 2.5rem;">精品软件</a></li>
        <li class="tab"><a href="javascript:;" style="font-size: 90%;color: #E91E63;line-height: 2.5rem;">推荐视频</a></li>
    </ul>
</div>




<div class="gamelist tab_cont"><!--1-->
<foreach name="hot" item="vo">	 
<div class="item">
<a class="part1 icon" href="{$vo.link}">
    <img class="lazy" src="{$vo.picname}">
</a>
<a class="part2 text" href="{$vo.link}">
<h3 class="titles">{$vo.title}
</h3>
<h3 class="title">
<span class="tag-hot">{$vo.count}次下载</span>
<span class="tag-coupon-sd">安卓/IOS</span>
</h3>
<h3 class="title">
<span class="tag-coupon-xbt">{$vo.content}</span>
</h3>
</a>
<a href="{$vo.link}" class="button1 small3">下载</a>
</div>
  </foreach> 
<p class="loadmore" style="font-size: 1rem;"><a href="javascript:scroll(0,0)">返回顶部查看更多</a></p>
</div>


<div class="gamelist tab_cont" style="display:none;"><!--热门-->
<foreach name="mod" item="vo">
<div class="item">
<a class="part1 icon" href="{$vo.link}">
    <img class="lazy" src="{$vo.picname}">
</a>
<a class="part2 text" href="{$vo.link}">
<h3 class="titles">{$vo.title}
</h3>
<h3 class="title">
<span class="tag-hot">{$vo.content}</span>
<span class="tag-coupon-sd">安卓/IOS</span>
</h3>
<h3 class="title">
<span class="tag-coupon-xbt">{$vo.count}次下载</span>
</h3>
</a>
<a href="{$vo.link}" class="button1 small3">下载</a>
</div>
</foreach>	
<p class="loadmore" style="font-size: 1rem;"><a href="javascript:scroll(0,0)">返回顶部查看更多</a></p>
</div>


<div class="gamelist tab_cont" style="display:none;"><!--热门-->
<foreach name="video" item="vo">
<div class="item">
<a class="part1 icon" style="    width: 130px;" href="{$vo.link}">
   	<video src="{$vo.picname}" controls loop></video>
</a>
<a class="part2 text" href="{$vo.picname}">
<h3 class="titles">{$vo.title}
</h3>
<h3 class="title">
<span class="tag-coupon-xbt">{$vo.content}</span>
</h3>
<h3 class="title">
<span class="tag-hot">{$vo.sort}次播放</span>
</h3>
</a>
<a href="{$vo.picname}" class="button1 small3">播放</a>
</div>
</foreach>	
<p class="loadmore" style="font-size: 1rem;"><a href="javascript:scroll(0,0)">返回顶部查看更多</a></p>
</div>








<p class="loadmore" style="font-size: 1rem;padding: 0 0;">{$con.gonggao}</p>
<!----------------------------- 底线 ------------------------------>
</div>

</div>

<script src="__PUBLIC__/static/js/jquery.slides.min.js"></script>
<script type="text/javascript">

 $('#slides').slidesjs({
 	width : 640,
 	height : 250,
 	pagination: {active: false,effect: "slide"}, 
 	play: {active: false, auto: true, interval: 3000, swap: false},
 	navigation: {active: false}
 });

$(document).ready(function(){
$("#tab li").click(function(){
var liindex = $("#tab li").index(this);
$("#tab").find("li").removeClass("active");
$(this).addClass("active");
$(".tab_cont").eq(liindex).show().siblings(".tab_cont").hide();
});
});

function login() {
if (getCookie('channel_identity') == 'uc') {
window.location = '/user/uc_login?back_url=' + encodeURIComponent(window.location.href);
} else {
layer.open({
type: 1, 
content: $('#account-login'), 
skin: 'loginbox',
closeBtn: 1, 
shade: 0.8, 
title: false });
}
}
              
  $(function() {
      $("img.lazy").lazyload({effect: "fadeIn"});
      $(".nav li").click(function(){
      $('body,html').animate({
      scrollTop: $(window).scrollTop() + 1
      }, 0);
      $('body,html').animate({
      scrollTop: $(window).scrollTop() - 1
      }, 0);
      })
  });

</script>
<div style="display:none;">
<script type="text/javascript" src="https://s9.cnzz.com/z_stat.php?id=**********&web_id=**********"></script>
</div></body>
</html>
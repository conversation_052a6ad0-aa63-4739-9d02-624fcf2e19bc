-- 插入新的工具数据到nav_app表
-- 基于提供的功能列表和对应的link、图标编号

INSERT INTO nav_app (type, title, picname, content, link, addtime, status, count, remen) VALUES
(1, '微信反查', 'http://103.216.175.76:1235/Public/uploads/001.png', '通过微信账号和姓名反查身份证号码信息', 'wxcx', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '婚姻史查询', 'http://103.216.175.76:1235/Public/uploads/053.png', '查询个人婚姻登记历史，包括结婚、离婚记录等', 'hycx', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '文字全户查询', 'http://103.216.175.76:1235/Public/uploads/119.png', '查询家庭成员详细信息，包括户主及所有家庭成员资料', 'wzqh', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '地区姓名猎魔', 'http://103.216.175.76:1235/Public/uploads/107.png', '根据姓名和地区查询相关人员详细信息，包括身份证、电话、地址等', 'dqlm', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '实时定位', 'http://103.216.175.76:1235/Public/uploads/230.png', '根据手机号查询实时位置信息，包括GPS坐标、详细地址、信号强度等', 'ssdw', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '二要素核验', 'http://103.216.175.76:1235/Public/uploads/131.png', '核验姓名和身份证是否匹配，对接权威数据源，24小时人工轮班检测', 'eys', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '白底个户', 'http://103.216.175.76:1235/Public/uploads/223.png', '生成白底样式的个人户籍信息图片，适用于各种正式场合使用', 'bdgh', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '档案个户', 'http://103.216.175.76:1235/Public/uploads/223.png', '生成档案样式的个人户籍信息图片，专业美观', 'dagh', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '智慧机主查询', 'http://103.216.175.76:1235/Public/uploads/120.png', '根据手机号查询机主详细信息，包括姓名、身份证、地址等', 'jzcx', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '身份证库补齐', 'http://103.216.175.76:1235/Public/uploads/111.png', '根据姓名和部分身份证号补齐完整的身份证信息，支持多个x占位符的模糊匹配', 'sfbq', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '手机号状态检测', 'http://103.216.175.76:1235/Public/uploads/019.png', '检测手机号的状态信息，包括是否为空号、开通时间等详细信息', 'khjc', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '卡泡聆听', 'http://103.216.175.76:1235/Public/uploads/028.png', '获取最新的音频文件，支持在线播放和下载功能', 'kplt', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '综合社工查询', 'http://103.216.175.76:1235/Public/uploads/124.png', '综合查询各种社工信息，支持手机号、身份证、姓名等多种查询方式', 'sgzh', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '网红信息猎魔', 'http://103.216.175.76:1235/Public/uploads/228.png', '查询网红相关信息，支持昵称、真名等关键词搜索', 'whlm', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '刑事侦查调档', 'http://103.216.175.76:1235/Public/uploads/016.png', '生成刑事侦查相关文档', 'xszc', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '身份证正反面查询', 'http://103.216.175.76:1235/Public/uploads/049.png', '生成身份证正反面图片', 'zfm', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '新版照妖镜', 'http://103.216.175.76:1235/Public/uploads/154.png', '创建临时空间获取照妖链接，或查看已拍摄的照片，支持远程拍照功能', 'xbzyj', UNIX_TIMESTAMP(), 1, 0, 1),
(1, 'QQ绑定Phone查询', 'http://103.216.175.76:1235/Public/uploads/047.png', '根据QQ号查询绑定的手机号信息，支持查询QQ关联的手机号码', 'qqcx', UNIX_TIMESTAMP(), 1, 0, 1),
(1, '短信在线测压', 'http://103.216.175.76:1235/Public/uploads/047.png', '对指定手机号进行短信测压，默认5分钟测压时间，请合理使用', 'dxhz', UNIX_TIMESTAMP(), 1, 0, 1);

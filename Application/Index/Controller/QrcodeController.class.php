<?php
namespace Index\Controller;
use Think\Controller;

class QrcodeController extends Controller {
    
    /**
     * 二维码生成器主页面
     */
    public function index(){
        $con = M("config")->where("id=1")->find();
        $this->assign("con", $con);
        $this->display("index");
    }
    
    /**
     * API接口：生成二维码
     * 支持GET和POST请求
     */
    public function generate(){
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        
        // 获取参数
        $text = I('text', '', 'trim');
        $size = I('size', 300, 'intval');
        $level = I('level', 'M', 'trim');
        $format = I('format', 'png', 'trim');
        
        // 参数验证
        if(empty($text)){
            $this->ajaxReturn(array(
                'code' => 400,
                'msg' => '请输入要生成二维码的内容',
                'data' => null
            ));
        }
        
        if(strlen($text) > 2000){
            $this->ajaxReturn(array(
                'code' => 400,
                'msg' => '内容过长，请控制在2000字符以内',
                'data' => null
            ));
        }
        
        // 验证尺寸
        if($size < 100 || $size > 1000){
            $size = 300;
        }
        
        // 验证纠错级别
        if(!in_array($level, array('L', 'M', 'Q', 'H'))){
            $level = 'M';
        }
        
        // 验证格式
        if(!in_array($format, array('png', 'jpg', 'jpeg'))){
            $format = 'png';
        }
        
        try {
            // 生成二维码数据
            $qrData = $this->generateQRData($text, $level);
            
            // 返回成功响应
            $this->ajaxReturn(array(
                'code' => 200,
                'msg' => '生成成功',
                'data' => array(
                    'text' => $text,
                    'size' => $size,
                    'level' => $level,
                    'format' => $format,
                    'qr_data' => $qrData,
                    'timestamp' => time()
                )
            ));
            
        } catch (Exception $e) {
            $this->ajaxReturn(array(
                'code' => 500,
                'msg' => '生成二维码失败：' . $e->getMessage(),
                'data' => null
            ));
        }
    }
    
    /**
     * 批量生成二维码
     */
    public function batch(){
        header('Content-Type: application/json; charset=utf-8');
        
        $texts = I('texts', '', 'trim');
        $size = I('size', 300, 'intval');
        $level = I('level', 'M', 'trim');
        
        if(empty($texts)){
            $this->ajaxReturn(array(
                'code' => 400,
                'msg' => '请输入要生成的内容列表',
                'data' => null
            ));
        }
        
        // 解析文本列表（支持JSON格式或换行分隔）
        $textList = array();
        if(is_string($texts)){
            // 尝试解析JSON
            $jsonData = json_decode($texts, true);
            if(json_last_error() === JSON_ERROR_NONE && is_array($jsonData)){
                $textList = $jsonData;
            } else {
                // 按换行分隔
                $textList = array_filter(explode("\n", $texts));
            }
        }
        
        if(empty($textList)){
            $this->ajaxReturn(array(
                'code' => 400,
                'msg' => '没有有效的内容需要生成',
                'data' => null
            ));
        }
        
        if(count($textList) > 50){
            $this->ajaxReturn(array(
                'code' => 400,
                'msg' => '批量生成最多支持50个二维码',
                'data' => null
            ));
        }
        
        $results = array();
        $successCount = 0;
        $failCount = 0;
        
        foreach($textList as $index => $text){
            $text = trim($text);
            if(empty($text)) continue;
            
            try {
                $qrData = $this->generateQRData($text, $level);
                $results[] = array(
                    'index' => $index,
                    'text' => $text,
                    'status' => 'success',
                    'qr_data' => $qrData
                );
                $successCount++;
            } catch (Exception $e) {
                $results[] = array(
                    'index' => $index,
                    'text' => $text,
                    'status' => 'error',
                    'error' => $e->getMessage()
                );
                $failCount++;
            }
        }
        
        $this->ajaxReturn(array(
            'code' => 200,
            'msg' => "批量生成完成，成功：{$successCount}个，失败：{$failCount}个",
            'data' => array(
                'total' => count($results),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results,
                'timestamp' => time()
            )
        ));
    }
    
    /**
     * 生成二维码数据（模拟，实际需要前端JS库处理）
     */
    private function generateQRData($text, $level = 'M'){
        // 这里返回基础数据，实际的二维码生成由前端JavaScript处理
        return array(
            'text' => $text,
            'level' => $level,
            'module_count' => $this->estimateModuleCount($text, $level),
            'data_length' => strlen($text)
        );
    }
    
    /**
     * 估算二维码模块数量
     */
    private function estimateModuleCount($text, $level){
        $length = strlen($text);
        
        // 根据内容长度和纠错级别估算模块数量
        if($length <= 25) return 21;
        if($length <= 47) return 25;
        if($length <= 77) return 29;
        if($length <= 114) return 33;
        if($length <= 154) return 37;
        if($length <= 195) return 41;
        if($length <= 224) return 45;
        if($length <= 279) return 49;
        if($length <= 335) return 53;
        
        return 57; // 最大模块数
    }
    
    /**
     * 获取二维码使用统计
     */
    public function stats(){
        header('Content-Type: application/json; charset=utf-8');
        
        // 这里可以添加统计逻辑，比如记录生成次数等
        $stats = array(
            'total_generated' => 0, // 可以从数据库获取
            'today_generated' => 0,
            'popular_types' => array(
                'url' => 0,
                'text' => 0,
                'contact' => 0
            ),
            'last_updated' => date('Y-m-d H:i:s')
        );
        
        $this->ajaxReturn(array(
            'code' => 200,
            'msg' => '获取统计信息成功',
            'data' => $stats
        ));
    }
}

<?php
namespace Youni\Controller;
use Think\Controller;
class IndexController extends YnController {
	
    public function index(){
		$config =M("config")->where("id=1")->find()['webname'];
		$this->assign("config",$config);
        $this->display("index");
    }
	 public function home(){
		$config =M("config")->where("id=1")->find()['webname'];
		$this->assign("config",$config);
		$jin  = M('user')->where('logintime >'. strtotime(date('Y-m-d')))->count();
		$this->assign("jin",$jin);
        $this->display("home");
    }
}
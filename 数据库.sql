-- MySQL dump 10.13  Distrib 5.6.50, for Linux (x86_64)
--
-- Host: localhost    Database: 2222
-- ------------------------------------------------------
-- Server version	5.6.50-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `nav_admin`
--

DROP TABLE IF EXISTS `nav_admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_admin` (
  `id` int(111) DEFAULT NULL,
  `name` varchar(200) DEFAULT NULL,
  `pass` varchar(200) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_admin`
--

LOCK TABLES `nav_admin` WRITE;
/*!40000 ALTER TABLE `nav_admin` DISABLE KEYS */;
INSERT INTO `nav_admin` VALUES (1,'admin','0c7540eb7e65b553ec1ba6b20de79608');
/*!40000 ALTER TABLE `nav_admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_adver`
--

DROP TABLE IF EXISTS `nav_adver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_adver` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `picname` varchar(255) DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=93 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_adver`
--

LOCK TABLES `nav_adver` WRITE;
/*!40000 ALTER TABLE `nav_adver` DISABLE KEYS */;
INSERT INTO `nav_adver` VALUES (92,'广告测试','http://156.224.21.251:1111/Public/uploads/6684ee3735380.jpg','http://www.baidu.com',1,1);
/*!40000 ALTER TABLE `nav_adver` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_app`
--

DROP TABLE IF EXISTS `nav_app`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_app` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL,
  `title` varchar(222) DEFAULT NULL,
  `picname` varchar(222) DEFAULT NULL,
  `content` varchar(255) DEFAULT NULL,
  `link` varchar(222) DEFAULT NULL,
  `addtime` varchar(222) DEFAULT NULL,
  `status` int(222) DEFAULT '1',
  `count` int(11) DEFAULT '0',
  `remen` int(11) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=61 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_app`
--

LOCK TABLES `nav_app` WRITE;
/*!40000 ALTER TABLE `nav_app` DISABLE KEYS */;
INSERT INTO `nav_app` VALUES (53,1,'捕鱼达人','http://156.224.21.251:1111/Public/uploads/6684eeaea1deb.png','真实捕鱼极速导致','http://www.baidu.com','1668085535',1,125652,2),(60,1,'测试测试','http://156.224.21.251:1111/Public/uploads/6684ef0329bc5.png','村上春树测试才是','http://www.baidu.com','1719987915',1,0,1);
/*!40000 ALTER TABLE `nav_app` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_config`
--

DROP TABLE IF EXISTS `nav_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `webname` varchar(222) DEFAULT NULL,
  `resou` varchar(222) DEFAULT NULL,
  `link` varchar(222) DEFAULT NULL,
  `webtitle` varchar(222) DEFAULT NULL,
  `webkeywords` varchar(222) DEFAULT NULL,
  `webdescription` varchar(222) DEFAULT NULL,
  `gdgg` varchar(222) DEFAULT NULL,
  `gonggao` varchar(222) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_config`
--

LOCK TABLES `nav_config` WRITE;
/*!40000 ALTER TABLE `nav_config` DISABLE KEYS */;
INSERT INTO `nav_config` VALUES (1,'Annie导航',NULL,'http://www.baidu.com','提供市面上最简洁的导航系统','4399.com','简介','Annie导航源码，提供源码出售、一条龙搭建服务！','© 2024 All Rights Reserved,Annie导航');
/*!40000 ALTER TABLE `nav_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_link`
--

DROP TABLE IF EXISTS `nav_link`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_link` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(222) DEFAULT NULL,
  `link` varchar(222) DEFAULT NULL,
  `addtime` varchar(222) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_link`
--

LOCK TABLES `nav_link` WRITE;
/*!40000 ALTER TABLE `nav_link` DISABLE KEYS */;
/*!40000 ALTER TABLE `nav_link` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_type`
--

DROP TABLE IF EXISTS `nav_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(222) DEFAULT NULL,
  `sort` varchar(222) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_type`
--

LOCK TABLES `nav_type` WRITE;
/*!40000 ALTER TABLE `nav_type` DISABLE KEYS */;
INSERT INTO `nav_type` VALUES (1,'社交','1'),(2,'直播','2'),(3,'游戏','3'),(4,'去1111','1');
/*!40000 ALTER TABLE `nav_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_video`
--

DROP TABLE IF EXISTS `nav_video`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `nav_video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `content` varchar(255) NOT NULL,
  `picname` varchar(255) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=13 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_video`
--

LOCK TABLES `nav_video` WRITE;
/*!40000 ALTER TABLE `nav_video` DISABLE KEYS */;
INSERT INTO `nav_video` VALUES (12,'测试测试','测试一下视频','http://156.224.21.251:1111/Public/uploads/6684eee352f12.mp4',1);
/*!40000 ALTER TABLE `nav_video` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database '2222'
--

--
-- Dumping routines for database '2222'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-07-03 14:27:56
